//挂载一个对象到window上
/**
 * @param {Object} option
 * @param {String} option.container 挂载的容器
 * @param {Object} option.iframeStyle iframe的样式
 * @param {Object} option.controls 控制器
 */
;(function (global) {
  let iframe = document.createElement('iframe')
  window.addEventListener('message', receiveMessage, false)
  // 处理接收到的消息
  function receiveMessage(event) {
    if (event.data === 'hide_chat') {
      document.querySelector('iframe.fsLiveChat').className = 'fsLiveChat'
    }
    if (event.data?.type === 'open_chat') {
      document.querySelector('iframe.fsLiveChat').className = 'fsLiveChat show'
    }
  }
  function getDooringApiStr(opt) {
    let controls = Object.assign(
      {
        //按照实际需求新增
      },
      opt || {}
    )
    if (["ru"].includes(controls["webSite"])) {
      controls["webSite"] = "en";
      controls["isoCode"] = "US";
      controls["language"] = 5;
    }
    let params = ''
    for (let key in controls) {
      params += key + '=' + encodeURI(controls[key]) + '&'
    }
    return params.slice(0, params.length - 1)
  }

  global.fsLiveChatMount = function (option) {
    this.option = option
    // const sdk_domain_path = '$_path' //项目地址
    const sdk_domain_path = "http://10.28.86.17:5174"; //项目地址
    iframe.src = sdk_domain_path + '?' + getDooringApiStr(option)
    iframe.allow = 'geolocation'
    iframe.style.border = 'none'
    iframe.className = 'fsLiveChat'
    iframe.title = 'FsLiveChat'
    if (option && option.iframeStyle) {
      iframe.style.border = option.iframeStyle.border || 'none'
      // iframe.style.width = option.iframeStyle.width || "100vw";
      // iframe.style.height = option.iframeStyle.height || "100vh";
    }

    document.querySelector(option.container || 'body').appendChild(iframe)
  }
  //卸载
  global.fsLiveChatUnmount = function () {
    document.querySelector(this.option.container || 'body').removeChild(iframe)
    window.removeEventListener('message', receiveMessage, false)
  }
  //显示
  global.showFsLiveChat = function () {
    document
      .querySelector('iframe.fsLiveChat')
      .contentWindow.postMessage(
        { type: 'open_chat', origin: window.location.href },
        '*'
      )
    document.querySelector('iframe.fsLiveChat').className = 'fsLiveChat show'
  }
  //隐藏
  global.hideFsLiveChat = function () {
    document
      .querySelector('iframe.fsLiveChat')
      .contentWindow.postMessage('hide_chat', '*')
    document.querySelector('iframe.fsLiveChat').className = 'fsLiveChat'
  }
  //
  global.postDataToChat = function (payload) {
    document
      .querySelector('iframe.fsLiveChat')
      .contentWindow.postMessage(
        { type: payload.type, data: payload.data },
        '*'
      )
  }
})(window)
