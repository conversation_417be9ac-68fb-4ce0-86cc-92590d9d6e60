/**
* setup是位于beforeCreate和created之间的一个新的生命周期钩子，在setup中我们可以使用props、data、methods、computed、watch等，但是不能使用this
*/
<script setup lang="ts">
import LiveChat from '@/views/LiveChat.vue';
import ZeChat from '@/views/ZeChat.vue';
import PrivacyPolicy from '@/views/components/PrivacyPolicy/index.vue';
import { onMounted, ref, inject, onBeforeUnmount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { dataLayerToParent, getQueryString, getAllQueryStrings } from './util/util';
import EventBus from './util/eventBus';
import { initSocket } from '@/plugins/socket';

const $bus = inject<EventBus>('eventBus')
const appId = getQueryString('appId')
const liveChatStatus = ref(appId !== '2' ? false : true) // liveChat的html是否显示
const componentStatus = ref(appId !== '2' ? false : true) // 组件的状态
const originUrl = ref('') // iframe的url
const productInfo = ref(null) // 产品信息

// 隐私政策相关状态
const showPrivacyPolicy = ref(false) // 是否显示隐私政策页面
const privacyPolicyAccepted = ref(false) // 用户是否已接受隐私政策
const isCheckingPrivacyPolicy = ref(false) // 是否正在检查隐私政策状态

// 计算属性：判断是否应该显示 LiveChat 组件
const shouldShowLiveChat = computed(() => {
  return componentStatus.value && privacyPolicyAccepted.value && !showPrivacyPolicy.value && !isCheckingPrivacyPolicy.value
})

// 检查隐私政策状态
const checkPrivacyPolicyStatus = () => {
  const { clientUserInfo } = getAllQueryStrings()

  // 如果有 clientUserInfo（外部登录用户），跳过隐私政策检查
  if (clientUserInfo) {
    console.log('外部登录用户，跳过隐私政策检查')
    privacyPolicyAccepted.value = true
    return
  }

  // 如果没有 clientUserInfo，需要查询内部用户的隐私政策状态
  console.log('内部用户，查询隐私政策状态')
  isCheckingPrivacyPolicy.value = true

  // 等待 WebSocket 连接建立后发送查询消息
  const checkConnection = () => {
    const ws = initSocket()
    const { status } = ws.getStatus()
    if (status === 2) { // 已连接
      // 发送 messageType 93 查询隐私政策状态
      ws.sendMsg({}, { messageType: 93 })
    } else {
      // 如果还没连接，等待一段时间后重试
      setTimeout(checkConnection, 500)
    }
  }

  checkConnection()
}

// WebSocket 消息处理
const handleWebSocketMessage = (msg: any) => {
  if (msg.messageType === 94) { // 隐私政策状态响应
    console.log('收到隐私政策状态响应:', msg)
    isCheckingPrivacyPolicy.value = false

    // 根据服务器返回的状态决定是否显示隐私政策页面
    const msgAny = msg as any
    const isAccepted = msgAny.privacyPolicyAccepted || msgAny.accepted || msgAny.hasAccepted || false

    if (isAccepted === true) {
      // 用户已接受隐私政策，直接进入聊天
      privacyPolicyAccepted.value = true
      showPrivacyPolicy.value = false
    } else {
      // 用户未接受隐私政策，显示隐私政策页面
      privacyPolicyAccepted.value = false
      showPrivacyPolicy.value = true
    }
  }
}

// 隐私政策操作处理
const handleAcceptPolicy = () => {
  privacyPolicyAccepted.value = true
  showPrivacyPolicy.value = false

  // 发送接受隐私政策的消息到服务器
  const ws = initSocket()
  ws.sendMsg({ accepted: true }, { messageType: 95 })

  console.log('用户接受了隐私政策')
}

const handleRejectPolicy = () => {
  // 拒绝后不进入聊天，但保持在隐私政策页面
  console.log('用户拒绝了隐私政策')
}

const handlePrivacyPolicyClose = () => {
  changeChatStatus('close')
}

// 语言设置
setLanguage()
onMounted(() => {
  // 检查隐私政策状态
  checkPrivacyPolicyStatus()

  // 设置 WebSocket 消息监听
  const ws = initSocket()
  ws.on('message', handleWebSocketMessage)

  // iframe 通信
  window.addEventListener('message', handleShowLiveChat)
  $bus.on('changeChatStatus', changeChatStatus)
  const storageStatus = localStorage.getItem('liveChatStatus')
  if (storageStatus === '1') {
    // 使用postMessage向父窗口通讯
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    componentStatus.value = true
    liveChatStatus.value = true
  }
});
const handleShowLiveChat = (event: MessageEvent) => {
  if (event.data?.type === 'open_chat') {
    // 当重新打开聊天时，重置WebSocket连接状态
    const ws = initSocket()
    ws.resetConnectionState()

    liveChatStatus.value = true
    componentStatus.value = true
    localStorage.setItem('liveChatStatus', '1')
    originUrl.value = event.data.origin
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    window.parent.postMessage({type: 'unread_message', data: false}, '*')
  } else if (event.data === 'hide_chat') {
    liveChatStatus.value = false
    localStorage.setItem('liveChatStatus', '0')
  }
  if (event.data.type === 'prod_card') {
    productInfo.value = event.data.data
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('message', handleShowLiveChat)
})
const changeChatStatus = (status: string) => {
  if (appId === '2') return localStorage.setItem('show_end_btn', 'false')
  window.parent.postMessage('hide_chat', '*')
  localStorage.setItem('liveChatStatus', '0')
  switch (status) {
    case 'minimize':
      setTimeout(() => {
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "minimize",
      })
      break;
  
    default:
      setTimeout(() => {
        componentStatus.value = false
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "close",
      })
      break;
  }
}
function setLanguage() {
  const { locale } = useI18n()
  const site = getQueryString('webSite') || 'en'
  locale.value = site
}
</script>

<template>
  <!-- 隐私政策页面（包含加载状态） -->
  <PrivacyPolicy
    v-if="componentStatus && (showPrivacyPolicy || isCheckingPrivacyPolicy)"
    :isLoading="isCheckingPrivacyPolicy"
    @accept-policy="handleAcceptPolicy"
    @reject-policy="handleRejectPolicy"
    @close-chat="handlePrivacyPolicyClose"
  />

  <!-- 正常聊天界面 -->
  <LiveChat
    v-else-if="shouldShowLiveChat"
    @changeChatStatus="changeChatStatus"
    :liveChatStatus="liveChatStatus"
    :originUrl="originUrl"
    :productInfo="productInfo"
  />
</template>

<style lang="scss">

</style>
