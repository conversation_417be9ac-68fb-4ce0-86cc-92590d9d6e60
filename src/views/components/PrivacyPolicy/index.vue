<template>
  <div class="live_chat_wrap" :class="isMobileBool ? 'isMobile' : ''">
    <!-- 加载状态 -->
    <div v-if="props.isLoading" class="privacy_loading">
      <GlobalLoading />
    </div>

    <!-- 隐私政策内容 -->
    <template v-else>
      <!-- 复用 ChatHeader 组件样式，但只保留关闭功能 -->
      <section class="live_chat_header">
        <div class="header_wrap">
          <div class="header_cs_status">
            {{ $c("popupTitle") }}
          </div>
          <div class="header_right">
            <div class="icon_wrap close_icon_wrap" @click="closeChat">
              <span class="iconfont iconfont_close">&#xf30a;</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 隐私政策内容区域 -->
      <div class="privacy_policy_content">
        <div class="privacy_policy_text">
          <template v-if="!isRejected">
            By choosing to accept, you expressly agree that FS.com, Inc., its affiliates, and service providers may record, store, share, and analyze the content of your chat conversations. For more information please review our Privacy Policy and Notice at Collection.
          </template>
          <template v-else>
            That's okay. You can view the policy any time and consent to continue
          </template>
        </div>

        <!-- 操作按钮 -->
        <div class="privacy_policy_buttons" v-if="!isRejected">
          <FsButton
            type="black"
            @click="acceptPolicy"
            class="accept_btn"
          >
            Yes, I Accept.
          </FsButton>
          <FsButton
            type="whiteline"
            @click="rejectPolicy"
            class="reject_btn"
          >
            No, Not now.
          </FsButton>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import FsButton from '@/components/FsButton.vue';
import GlobalLoading from '@/components/GlobalLoading.vue';
import { $c } from '@/plugins/c-inject';
import { isMobile } from '@/util/util';

// Props
interface Props {
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
});

const emit = defineEmits(['accept-policy', 'reject-policy', 'close-chat']);

const isRejected = ref(false);

// 移动端检测
const isMobileBool = computed(() => isMobile());

const acceptPolicy = () => {
  emit('accept-policy');
};

const rejectPolicy = () => {
  isRejected.value = true;
  emit('reject-policy');
};

const closeChat = () => {
  emit('close-chat');
};
</script>

<style scoped lang="scss">
// 导入 LiveChat 的样式，确保容器样式完全一致
@import '@/style/liveChat.scss';

// 加载状态样式
.privacy_loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #fff;
  flex: 1;
}

// 隐私政策内容区域样式
.privacy_policy_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 24px;
  text-align: center;
  background-color: #fff;
}

.privacy_policy_text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 32px;
  max-width: 500px;
}

.privacy_policy_buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 300px;

  .accept_btn,
  .reject_btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .privacy_policy_content {
    padding: 24px 16px;
  }

  .privacy_policy_text {
    font-size: 14px;
    margin-bottom: 24px;
  }

  .privacy_policy_buttons {
    max-width: 100%;

    .accept_btn,
    .reject_btn {
      height: 40px;
      font-size: 14px;
    }
  }
}
</style>
