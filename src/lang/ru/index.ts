export default {
  popupTitle: "Чат с нами",
  placeholder: "Введите здесь текст...",
  fileType: "Файл/картинка",
  chatNow: "Чат сейчас",
  endChat: "Закончить чат?",
  closeTips: "Вы действительно хотите закрыть чат?",
  closeBtn: "Подтвердить",
  feedbackTitle: "Оцените, пожалуйста, этот чат.",
  good: "Хоро<PERSON><PERSON>",
  bad: "Плохой",
  rateTip: "Вы поставили оценку",
  hasVoted: "Спасибо за ваш голос!",
  commentTitle: "У вас есть какие-либо отзывы об этом?",
  ratePlaceholder: "Введите ваши комментарии...",
  submit: "Отправить",
  read: "Прочитано",
  unread: "Непрочитано",
  emailTranscript: "Отправить историю чата",
  mute: "Немой",
  unmute: "Включить звук",
  emailTip:
    "Пожалуйста, введите адрес электронной почты, на который вы хотите отправить расшифровку этого разговора.",
  sendBtn: "Отправить",
  sendStatus: "Электронная почта отправлена",
  sendInfoStart:
    "Стенограмма этого разговора была отправлена по электронной почте ",
  sendInfoEnd:
    ". Если вы не получили электронное письмо в течение нескольких минут, пожалуйста, проверьте папку со спамом.",
  backChat: "Вернуться в чат",
  vaildInfo:
    "Извините, кажется, это не действительный адрес электронной почты.",
  message: "Сообщение",
  messages: "Сообщениея",
  downloadApp: "Скачать приложение FS",
  more3Files: "Загрузить до 3 файлов",
  fileSize5M: "Максимальный размер файла 5M",
  fileSize20M: "Максимальный размер файла 20M",
  invalidFileType: "Неверный тип файла",
  robot: "Виртуальный ассистент FS",
  advisor: "Советник FS",
  typing: "Служба поддержки пишет...",
  newFeedback: {
    title: "Оцените разговор",
    comment: "комментарий",
    update: "Обновление",
  },
  userForm: {
    name: "Name",
    email: "Email address",
    selectAgent: "Select agent",
    selectOption: ["Product purchase", "Technical support"],
    saved: "Saved",
  },
  placeholder2: "Write a reply...",
  viewPastConversations: "Просмотр прошлых разговоров",
  conversationHistory: "История разговоров",
  videoTip: {
    title: "Watch Video",
    text: "Watch the Video in New Window?",
    btn: "Start Now",
  },
  visitorUser: "Посетитель",
};
