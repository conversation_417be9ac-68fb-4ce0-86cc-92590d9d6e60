interface FormLabel {
  name: string;
  email: string;
  tel: string;
  help: string;
  Optional: string;
}

interface FormError {
  name: string;
  email: {
    error1: string;
    error2: string;
  };
  tel: string;
  help: string;
}

interface NewFeedback {
  title: string;
  comment: string;
  update: string;
}

interface UserForm {
  name: string;
  email: string;
  selectAgent: string;
  selectOption: string[];
  saved: string;
}

interface VideoTip {
  title: string;
  text: string;
  btn: string;
}

interface DutchTranslation {
  popupTitle: string;
  placeholder: string;
  fileType: string;
  chatNow: string;
  endChat: string;
  closeTips: string;
  closeBtn: string;
  feedbackTitle: string;
  good: string;
  bad: string;
  rateTip: string;
  hasVoted: string;
  commentTitle: string;
  ratePlaceholder: string;
  submit: string;
  read: string;
  unread: string;
  emailTranscript: string;
  mute: string;
  unmute: string;
  emailTip: string;
  sendBtn: string;
  sendStatus: string;
  sendInfoStart: string;
  sendInfoEnd: string;
  backChat: string;
  vaildInfo: string;
  message: string;
  messages: string;
  downloadApp: string;
  more3Files: string;
  fileSize5M: string;
  fileSize20M: string;
  invalidFileType: string;
  robot: string;
  advisor: string;
  formLabel: FormLabel;
  formError: FormError;
  typing: string;
  newFeedback: NewFeedback;
  userForm: UserForm;
  placeholder2: string;
  videoTip: VideoTip;
}

const dutchTranslation = {
  popupTitle: "Chat met ons",
  placeholder: "Voer hier tekst in...",
  fileType: "Lokale bestand/Afbeelding",
  chatNow: "Nu chatten",
  endChat: "Chat beëindigen?",
  closeTips: "Wil je de chat echt sluiten?",
  closeBtn: "Sluiten",
  feedbackTitle: "Beoordeel je gesprek",
  good: "Goed",
  bad: "Slecht",
  rateTip: "Je hebt beoordeeld",
  hasVoted: "Bedankt voor je stem!",
  commentTitle: "Heb je feedback?",
  ratePlaceholder: "Typ je opmerkingen...",
  submit: "Verzenden",
  read: "Gelezen",
  unread: "Ongelezen",
  emailTranscript: "Stuur dit transcript per e-mail",
  mute: "Dempen",
  unmute: "Dempen opheffen",
  emailTip: "Voer het e-mailadres in waarnaar je het transcript van dit gesprek wilt sturen",
  sendBtn: "E-mail verzenden",
  sendStatus: "E-mail verzonden",
  sendInfoStart: "Een transcript van dit gesprek is gemaild naar ",
  sendInfoEnd: ". Als je binnen enkele minuten geen e-mail ontvangt, controleer dan je spammap.",
  backChat: "Ga terug naar gesprek",
  vaildInfo: "Sorry, het lijkt geen geldig e-mailadres te zijn.",
  message: "Bericht",
  messages: "Berichten",
  downloadApp: "Download FS App",
  more3Files: "Upload maximaal 3 bestanden",
  fileSize5M: "Maximale bestandsgrootte 5M.",
  fileSize20M: "Maximale bestandsgrootte 20M.",
  invalidFileType: "Ongeldig bestandstype.",
  robot: "FS Virtuele Assistent",
  advisor: "FS Adviseur",
  formLabel: {
    name: "Naam (Optioneel)",
    email: "E-mailadres",
    tel: "Telefoonnummer",
    help: "Hoe kunnen we helpen?",
    Optional: "Optioneel"
  },
  formError: {
    name: "",
    email: {
      error1: "Dit veld is verplicht.",
      error2: "Voer een geldig e-mailadres in."
    },
    tel: "",
    help: "Dit veld is verplicht."
  },
  typing: "Agent is aan het typen...",
  newFeedback: {
    title: "Beoordeel het gesprek",
    comment: "Opmerking",
    update: "Bijwerken"
  },
  userForm: {
    name: "Naam",
    email: "E-mailadres",
    selectAgent: "Selecteer agent",
    selectOption: [
      "Productaankoop",
      "Technische ondersteuning"
    ],
    saved: "Opgeslagen"
  },
  placeholder2: "Schrijf een antwoord...",
  videoTip: {
    title: "Bekijk video",
    text: "Bekijk de video in een nieuw venster?",
    btn: "Start nu"
  }
};

export default dutchTranslation;